import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { 
  User, 
  Mail, 
  Linkedin, 
  Building, 
  Globe, 
  Target, 
  DollarSign, 
  MapPin, 
  TrendingUp,
  MessageSquare,
  CheckCircle
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface InvestorFormData {
  investor_name: string;
  investor_email: string;
  investor_linkedin_url: string;
  firm_name: string;
  firm_website_url: string;
  investor_type: string;
  investment_stages: string[];
  preferred_verticals: string[];
  geography_preference: string;
  average_check_size: string;
  thesis_keywords: string;
  source: string;
  investor_wechat_id_cn: string;
  investor_wechat_official_account: string;
  focus_on_chuhai_cn: boolean;
}

const InvestorForm = () => {
  const [formData, setFormData] = useState<InvestorFormData>({
    investor_name: '',
    investor_email: '',
    investor_linkedin_url: '',
    firm_name: '',
    firm_website_url: '',
    investor_type: '',
    investment_stages: [],
    preferred_verticals: [],
    geography_preference: '',
    average_check_size: '',
    thesis_keywords: '',
    source: '',
    investor_wechat_id_cn: '',
    investor_wechat_official_account: '',
    focus_on_chuhai_cn: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const investorTypes = [
    'Angel Investor',
    'Venture Capital',
    'Private Equity',
    'Corporate VC',
    'Family Office',
    'Accelerator',
    'Incubator',
    'Government Fund',
    'Other'
  ];

  const investmentStages = [
    'Pre-seed',
    'Seed',
    'Series A',
    'Series B',
    'Series C',
    'Series D+',
    'Growth',
    'Late Stage',
    'IPO'
  ];

  const verticals = [
    'AI/ML',
    'Fintech',
    'Healthcare',
    'Biotech',
    'SaaS',
    'E-commerce',
    'Edtech',
    'Proptech',
    'Cleantech',
    'Mobility',
    'Gaming',
    'Consumer',
    'Enterprise',
    'Hardware',
    'Blockchain',
    'Cybersecurity',
    'Other'
  ];

  const geographyOptions = [
    'Global',
    'North America',
    'United States',
    'Canada',
    'Europe',
    'Asia Pacific',
    'China',
    'Southeast Asia',
    'India',
    'Latin America',
    'Middle East',
    'Africa',
    'Other'
  ];

  const checkSizeOptions = [
    'Under $25K',
    '$25K - $100K',
    '$100K - $500K',
    '$500K - $1M',
    '$1M - $5M',
    '$5M - $10M',
    '$10M - $25M',
    '$25M - $50M',
    '$50M+',
    'Varies'
  ];

  const handleInputChange = (field: keyof InvestorFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayFieldChange = (field: 'investment_stages' | 'preferred_verticals', value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...prev[field], value]
        : prev[field].filter(item => item !== value)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Here you would typically send the data to your backend/NocoDB
      console.log('Investor form submitted:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Registration successful! Welcome to our investor network.');
      
      // Reset form
      setFormData({
        investor_name: '',
        investor_email: '',
        investor_linkedin_url: '',
        firm_name: '',
        firm_website_url: '',
        investor_type: '',
        investment_stages: [],
        preferred_verticals: [],
        geography_preference: '',
        average_check_size: '',
        thesis_keywords: '',
        source: '',
        investor_wechat_id_cn: '',
        investor_wechat_official_account: '',
        focus_on_chuhai_cn: false,
      });
      
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] shadow-lg sticky top-0 z-50 relative overflow-hidden">
        {/* Crystal glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="Veridian Vista Logo"
                  className="w-10 h-10 object-contain drop-shadow-lg"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Veridian Vista
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-white/80 hover:text-white font-medium">Home</a>
              <a href="/founder" className="text-white/80 hover:text-white font-medium">Founder</a>
              <a href="/investor" className="text-white hover:text-white font-medium">Investor</a>
              <a href="/about" className="text-white/80 hover:text-white font-medium">About Us</a>
            </nav>

            <a
              href="/"
              className="bg-white text-[#40826D] hover:bg-gray-100 font-medium px-6 py-2 rounded-md transition-colors"
            >
              Back to Home
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Join Our Investor Network</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Connect with innovative startups and exceptional founders. Complete your profile to access our curated deal flow and discover the next generation of breakthrough companies.
            </p>
          </div>

          <Card className="bg-white shadow-2xl border-0 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] text-white p-8">
              <CardTitle className="text-3xl flex items-center justify-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-7 h-7" />
                </div>
                <span>Investor Profile</span>
              </CardTitle>
              <p className="text-center text-white/90 text-lg mt-3">
                Tell us about yourself and your investment preferences
              </p>
            </CardHeader>

          <CardContent className="p-12">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Required Fields Note */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <p className="text-sm text-blue-800">
                  Fields marked with a red asterisk (*) are required.
                </p>
              </div>

              {/* Personal Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <User className="w-5 h-5 text-[#40826D]" />
                  <span>Personal Information</span>
                </h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="investor_name" className="text-gray-700 font-medium">
                      Full Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="investor_name"
                      value={formData.investor_name}
                      onChange={(e) => handleInputChange('investor_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_email" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Mail className="w-4 h-4" />
                      <span>Email Address <span className="text-red-500">*</span></span>
                    </Label>
                    <Input
                      id="investor_email"
                      type="email"
                      value={formData.investor_email}
                      onChange={(e) => handleInputChange('investor_email', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_linkedin_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Linkedin className="w-4 h-4" />
                      <span>LinkedIn Profile</span>
                    </Label>
                    <Input
                      id="investor_linkedin_url"
                      type="url"
                      value={formData.investor_linkedin_url}
                      onChange={(e) => handleInputChange('investor_linkedin_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="https://linkedin.com/in/yourprofile"
                    />
                  </div>
                </div>
              </div>

              {/* Firm Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Building className="w-5 h-5 text-[#40826D]" />
                  <span>Firm Information</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="firm_name" className="text-gray-700 font-medium">
                      Firm Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="firm_name"
                      value={formData.firm_name}
                      onChange={(e) => handleInputChange('firm_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="firm_website_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>Firm Website</span>
                    </Label>
                    <Input
                      id="firm_website_url"
                      type="url"
                      value={formData.firm_website_url}
                      onChange={(e) => handleInputChange('firm_website_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="https://yourfirm.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_type" className="text-gray-700 font-medium">
                      Investor Type <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.investor_type} onValueChange={(value) => handleInputChange('investor_type', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder="Select investor type" />
                      </SelectTrigger>
                      <SelectContent>
                        {investorTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Investment Preferences */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Target className="w-5 h-5 text-[#40826D]" />
                  <span>Investment Preferences</span>
                </h3>

                <div className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-gray-700 font-medium">Investment Stages</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {investmentStages.map((stage) => (
                        <div key={stage} className="flex items-center space-x-2">
                          <Checkbox
                            id={`stage-${stage}`}
                            checked={formData.investment_stages.includes(stage)}
                            onCheckedChange={(checked) =>
                              handleArrayFieldChange('investment_stages', stage, checked as boolean)
                            }
                          />
                          <Label htmlFor={`stage-${stage}`} className="text-sm text-gray-600">
                            {stage}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-gray-700 font-medium">Preferred Verticals</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {verticals.map((vertical) => (
                        <div key={vertical} className="flex items-center space-x-2">
                          <Checkbox
                            id={`vertical-${vertical}`}
                            checked={formData.preferred_verticals.includes(vertical)}
                            onCheckedChange={(checked) =>
                              handleArrayFieldChange('preferred_verticals', vertical, checked as boolean)
                            }
                          />
                          <Label htmlFor={`vertical-${vertical}`} className="text-sm text-gray-600">
                            {vertical}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="geography_preference" className="text-gray-700 font-medium flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>Geography Preference</span>
                      </Label>
                      <Select value={formData.geography_preference} onValueChange={(value) => handleInputChange('geography_preference', value)}>
                        <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                          <SelectValue placeholder="Select geography" />
                        </SelectTrigger>
                        <SelectContent>
                          {geographyOptions.map((geo) => (
                            <SelectItem key={geo} value={geo}>
                              {geo}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="average_check_size" className="text-gray-700 font-medium flex items-center space-x-1">
                        <DollarSign className="w-4 h-4" />
                        <span>Average Check Size</span>
                      </Label>
                      <Select value={formData.average_check_size} onValueChange={(value) => handleInputChange('average_check_size', value)}>
                        <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                          <SelectValue placeholder="Select check size range" />
                        </SelectTrigger>
                        <SelectContent>
                          {checkSizeOptions.map((size) => (
                            <SelectItem key={size} value={size}>
                              {size}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="thesis_keywords" className="text-gray-700 font-medium flex items-center space-x-1">
                      <MessageSquare className="w-4 h-4" />
                      <span>Investment Thesis Keywords</span>
                    </Label>
                    <Textarea
                      id="thesis_keywords"
                      value={formData.thesis_keywords}
                      onChange={(e) => handleInputChange('thesis_keywords', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="Describe your investment thesis, key focus areas, or keywords that define your investment strategy..."
                      rows={4}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-[#40826D]" />
                  <span>Additional Information</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="source" className="text-gray-700 font-medium">
                      How did you hear about us?
                    </Label>
                    <Input
                      id="source"
                      value={formData.source}
                      onChange={(e) => handleInputChange('source', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="e.g., LinkedIn, referral, conference, etc."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_wechat_id_cn" className="text-gray-700 font-medium">
                      WeChat ID (China)
                    </Label>
                    <Input
                      id="investor_wechat_id_cn"
                      value={formData.investor_wechat_id_cn}
                      onChange={(e) => handleInputChange('investor_wechat_id_cn', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="Your WeChat ID"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="investor_wechat_official_account" className="text-gray-700 font-medium">
                      WeChat Official Account
                    </Label>
                    <Input
                      id="investor_wechat_official_account"
                      value={formData.investor_wechat_official_account}
                      onChange={(e) => handleInputChange('investor_wechat_official_account', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="Official account name"
                    />
                  </div>

                  <div className="flex items-center space-x-3">
                    <Switch
                      id="focus_on_chuhai_cn"
                      checked={formData.focus_on_chuhai_cn}
                      onCheckedChange={(checked) => handleInputChange('focus_on_chuhai_cn', checked)}
                    />
                    <Label htmlFor="focus_on_chuhai_cn" className="text-gray-700 font-medium">
                      Focus on Chinese companies going global (出海)
                    </Label>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-center pt-8">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white px-12 py-3 text-lg font-semibold"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5 mr-2" />
                      Join Investor Network
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
        </div>
      </section>
    </div>
  );
};

export default InvestorForm;
