
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Target, BarChart3, Users, Globe } from 'lucide-react';

const Business = () => {
  const handleContactUs = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] shadow-lg sticky top-0 z-50 relative overflow-hidden">
        {/* Crystal glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rg<PERSON>(255,255,255,0.1)_0%,transparent_70%)]"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 flex items-center justify-center">
                <img
                  src="/veridian-logo.png"
                  alt="Veridian Vista Logo"
                  className="w-10 h-10 object-contain drop-shadow-lg"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Veridian Vista
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-white/80 hover:text-white font-medium">Home</a>
              <a href="/founders-lode" className="text-white/80 hover:text-white font-medium">Founder</a>
              <a href="/angel-veins" className="text-white/80 hover:text-white font-medium">Investor</a>
              <a href="/about" className="text-white/80 hover:text-white font-medium">About Us</a>
            </nav>

            <Button
              className="bg-white text-[#40826D] hover:bg-gray-100 font-medium px-6 py-2"
              onClick={handleContactUs}
            >
              Contact Us
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Transform Your Investment Strategy</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Veridian Vista leverages cutting-edge AI and big data analytics to revolutionize how investors discover and evaluate early-stage opportunities in today's complex market landscape.
          </p>
        </div>

        {/* Product Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <Card className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="text-center">
              <Target className="w-16 h-16 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D] text-2xl">Angel Veins</CardTitle>
              <p className="text-gray-600">Discover and connect with quality investors worldwide</p>
            </CardHeader>
            <CardContent className="text-center">
              <Button
                onClick={() => window.location.href = '/angel-veins'}
                className="bg-[#40826D] hover:bg-[#40826D]/90 text-white px-6 py-3"
              >
                Learn More
              </Button>
            </CardContent>
          </Card>

          <Card className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="text-center">
              <Users className="w-16 h-16 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D] text-2xl">Founder's Lode</CardTitle>
              <p className="text-gray-600">Connect with innovative founders and startup opportunities</p>
            </CardHeader>
            <CardContent className="text-center">
              <Button
                onClick={() => window.location.href = '/founders-lode'}
                className="bg-[#40826D] hover:bg-[#40826D]/90 text-white px-6 py-3"
              >
                Learn More
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">The Challenge</h2>
            <p className="text-gray-600 mb-4">
              Traditional investment discovery methods are time-consuming, inefficient, and often miss the most promising early-stage opportunities. Investors struggle with:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• Information overload from multiple sources</li>
              <li>• Lack of standardized evaluation criteria</li>
              <li>• Limited global market visibility</li>
              <li>• Time-intensive manual research processes</li>
            </ul>
          </div>
          
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Our Solution</h2>
            <p className="text-gray-600 mb-4">
              Veridian Vista addresses these challenges through advanced AI-powered investment intelligence:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• AI-driven opportunity scoring and ranking</li>
              <li>• Comprehensive global market coverage</li>
              <li>• Real-time data aggregation and analysis</li>
              <li>• Intelligent matching algorithms</li>
            </ul>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Target className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Precision Targeting</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Identify high-potential investments with 95% accuracy using our proprietary AI algorithms.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <BarChart3 className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Data Intelligence</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Process millions of data points in real-time to surface actionable investment insights.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Users className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Network Effects</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Connect with a curated network of top-tier investors and industry experts.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Globe className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Global Reach</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Access investment opportunities across 50+ countries and emerging markets.</p>
            </CardContent>
          </Card>
        </div>

        <div className="bg-gray-50 rounded-lg p-8 mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">Success Stories</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-[#40826D] mb-2">85%</div>
              <p className="text-gray-600">Reduction in research time</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-[#40826D] mb-2">3.2x</div>
              <p className="text-gray-600">Increase in deal flow quality</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-[#40826D] mb-2">$2.4B</div>
              <p className="text-gray-600">Total investments facilitated</p>
            </div>
          </div>
        </div>

        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to Transform Your Investment Process?</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join forward-thinking investors who are already using Veridian Vista to discover the next generation of breakthrough companies.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-[#40826D] hover:bg-[#40826D]/90">
              Schedule a Demo
            </Button>
            <Button size="lg" variant="outline" className="border-[#40826D] text-[#40826D] hover:bg-[#40826D]/10">
              Request Information
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Business;
