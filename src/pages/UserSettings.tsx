
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { User, Settings, LogOut, Edit, Save, X } from 'lucide-react';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: 'investor' | 'founder';
  tier: string;
  joinDate: string;
  country: string;
  company?: string;
}

const UserSettings = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [user, setUser] = useState<UserProfile>({
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'investor',
    tier: 'premium',
    joinDate: '2024-01-15',
    country: 'United States',
    company: 'Tech Ventures Inc.'
  });

  const [editedUser, setEditedUser] = useState<UserProfile>(user);

  const handleEdit = () => {
    setIsEditing(true);
    setEditedUser(user);
  };

  const handleSave = () => {
    setUser(editedUser);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedUser(user);
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setEditedUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-slate-800 mb-2">User Settings</h1>
        <p className="text-slate-600">Manage your profile and account settings</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Card */}
        <div className="lg:col-span-2">
          <Card className="bg-white/90 backdrop-blur-sm border border-slate-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Profile Information</span>
                </CardTitle>
                {!isEditing ? (
                  <Button onClick={handleEdit} variant="outline" size="sm">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button onClick={handleSave} size="sm" className="bg-teal-600 hover:bg-teal-700">
                      <Save className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                    <Button onClick={handleCancel} variant="outline" size="sm">
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Full Name</label>
                  {isEditing ? (
                    <Input
                      value={editedUser.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                    />
                  ) : (
                    <p className="text-slate-800 font-medium">{user.name}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                  {isEditing ? (
                    <Input
                      value={editedUser.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      type="email"
                    />
                  ) : (
                    <p className="text-slate-800 font-medium">{user.email}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Role</label>
                  <p className="text-slate-800 font-medium capitalize">{user.role}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Tier</label>
                  <p className="text-slate-800 font-medium capitalize">{user.tier}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Country</label>
                  {isEditing ? (
                    <Input
                      value={editedUser.country}
                      onChange={(e) => handleInputChange('country', e.target.value)}
                    />
                  ) : (
                    <p className="text-slate-800 font-medium">{user.country}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Company</label>
                  {isEditing ? (
                    <Input
                      value={editedUser.company || ''}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                    />
                  ) : (
                    <p className="text-slate-800 font-medium">{user.company || 'Not specified'}</p>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">Member Since</label>
                <p className="text-slate-800 font-medium">{new Date(user.joinDate).toLocaleDateString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <Card className="bg-white/90 backdrop-blur-sm border border-slate-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Quick Actions</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <User className="w-4 h-4 mr-2" />
                View Profile on Map
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Account Settings
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-white/90 backdrop-blur-sm border border-slate-200">
            <CardHeader>
              <CardTitle>Account Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-600">Status:</span>
                  <span className="font-medium text-green-600">Active</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Plan:</span>
                  <span className="font-medium capitalize">{user.tier}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Connections:</span>
                  <span className="font-medium">24</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UserSettings;
