
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Lightbulb, Rocket, TrendingUp, Network } from 'lucide-react';

const FoundersLode = () => {
  const handleContactUs = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] shadow-lg sticky top-0 z-50 relative overflow-hidden">
        {/* Crystal glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 flex items-center justify-center">
                <img
                  src="/veridian-logo.png"
                  alt="Veridian Vista Logo"
                  className="w-10 h-10 object-contain drop-shadow-lg"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Veridian Vista
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-white/80 hover:text-white font-medium">Home</a>
              <a href="/founder" className="text-white/80 hover:text-white font-medium">Founder</a>
              <a href="/investor" className="text-white/80 hover:text-white font-medium">Investor</a>
              <a href="/about" className="text-white/80 hover:text-white font-medium">About Us</a>
            </nav>

            <Button
              className="bg-white text-[#40826D] hover:bg-gray-100 font-medium px-6 py-2"
              onClick={handleContactUs}
            >
              Contact Us
            </Button>
          </div>
        </div>
      </header>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Founder's Lode - Startup Discovery</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Connect with innovative founders and discover the next generation of breakthrough startups.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">For Investors</h2>
            <p className="text-gray-600 mb-4">
              Discover high-potential startups and exceptional founders before they hit the mainstream:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• Early access to pre-seed and seed-stage startups</li>
              <li>• Detailed founder profiles and track records</li>
              <li>• Market analysis and competitive landscape</li>
              <li>• Direct communication with founding teams</li>
            </ul>
          </div>

          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">For Founders</h2>
            <p className="text-gray-600 mb-4">
              Showcase your vision and connect with the right investors and partners:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• Create comprehensive founder and company profiles</li>
              <li>• Access to verified investor network</li>
              <li>• Connect with co-founders and advisors</li>
              <li>• Gain visibility in the startup ecosystem</li>
            </ul>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Lightbulb className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Innovation Tracking</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Identify breakthrough technologies and disruptive business models early.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Rocket className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Growth Potential</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">AI-powered analysis of startup growth potential and market opportunity.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <TrendingUp className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Market Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Real-time market trends and emerging opportunities across industries.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Network className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Ecosystem Mapping</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Visualize connections between founders, companies, and investors.</p>
            </CardContent>
          </Card>
        </div>

        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Discover Tomorrow's Leaders Today</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join the platform where exceptional founders meet visionary investors to build the future.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-[#40826D] hover:bg-[#40826D]/90 text-white font-semibold"
              onClick={() => window.location.href = '/founder'}
            >
              Join as Founder
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-[#40826D] text-[#40826D] hover:bg-[#40826D]/10 font-semibold"
              onClick={() => window.location.href = '/investor'}
            >
              Join as Investor
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoundersLode;
