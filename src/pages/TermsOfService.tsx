import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '@/components/Header';

const TermsOfService = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="prose prose-lg max-w-none">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">
            {t('termsOfService.title')}
          </h1>
          
          <p className="text-gray-600 mb-8">
            {t('termsOfService.lastUpdated')}: {t('termsOfService.date')}
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.acceptance.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.acceptance.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.services.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.services.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.userObligations.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                {t('termsOfService.userObligations.content')}
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>{t('termsOfService.userObligations.items.accurate')}</li>
                <li>{t('termsOfService.userObligations.items.lawful')}</li>
                <li>{t('termsOfService.userObligations.items.respectful')}</li>
                <li>{t('termsOfService.userObligations.items.confidential')}</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.intellectualProperty.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.intellectualProperty.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.limitation.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.limitation.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.termination.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.termination.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.changes.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.changes.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('termsOfService.contact.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('termsOfService.contact.content')}
              </p>
              <p className="text-gray-700 mt-4">
                <strong>{t('termsOfService.contact.email')}</strong>: <EMAIL>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
