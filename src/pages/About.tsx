
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import { Linkedin, Mail } from 'lucide-react';

const About = () => {
  const handleContactUs = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">About Veridian Vista</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're on a mission to democratize investment intelligence through cutting-edge AI technology,
            helping investors discover the next generation of breakthrough companies.
          </p>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">Our Story</h2>
          <div className="max-w-4xl mx-auto text-gray-600 space-y-6">
            <p>
              Founded in 2025, Veridian Vista emerged from a simple observation: the most promising early-stage investments 
              often remain hidden in the noise of today's information-saturated market. Traditional investment discovery 
              methods were failing to identify these "green gems" - the high-potential opportunities that could deliver 
              exceptional returns.
            </p>
            <p>
              Our founding team, comprising former investment professionals and AI researchers, recognized that advanced 
              data science and machine learning could transform how investors discover and evaluate opportunities. We built 
              Veridian Vista to bridge this gap, creating an intelligent platform that turns market complexity into 
              competitive advantage.
            </p>
            <p>
              Today, we serve forward-thinking investors across the globe, from boutique venture capital firms to family 
              offices and institutional investors, all united by the desire to discover the next breakthrough companies 
              before they become obvious to everyone else.
            </p>
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">Our Values</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-white border border-gray-200 shadow-lg text-center">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-[#40826D] mb-3">Innovation</h3>
                <p className="text-gray-600">
                  We continuously push the boundaries of what's possible with AI and data science in investment discovery.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg text-center">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-[#40826D] mb-3">Transparency</h3>
                <p className="text-gray-600">
                  We believe in clear, explainable AI that helps investors understand not just what to invest in, but why.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg text-center">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-[#40826D] mb-3">Impact</h3>
                <p className="text-gray-600">
                  We're committed to democratizing access to high-quality investment intelligence for investors of all sizes.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">Leadership Team</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card className="bg-white border border-gray-200 shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-[#40826D]/10 border border-[#40826D]/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-xl font-bold text-[#40826D]">JS</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Jane Smith</h3>
                <p className="text-[#40826D] mb-2">CEO & Co-Founder</p>
                <p className="text-sm text-gray-600 mb-4">
                  Former partner at Sequoia Capital with 15+ years in venture capital and early-stage investing.
                </p>
                <div className="flex justify-center space-x-2">
                  <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                  <Mail className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-[#40826D]/10 border border-[#40826D]/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-xl font-bold text-[#40826D]">MC</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Michael Chen</h3>
                <p className="text-[#40826D] mb-2">CTO & Co-Founder</p>
                <p className="text-sm text-gray-600 mb-4">
                  Former AI research scientist at DeepMind with expertise in machine learning and data science.
                </p>
                <div className="flex justify-center space-x-2">
                  <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                  <Mail className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-[#40826D]/10 border border-[#40826D]/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-xl font-bold text-[#40826D]">EP</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Emily Park</h3>
                <p className="text-[#40826D] mb-2">VP of Product</p>
                <p className="text-sm text-gray-600 mb-4">
                  Former product leader at Bloomberg Terminal with deep expertise in financial data platforms.
                </p>
                <div className="flex justify-center space-x-2">
                  <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                  <Mail className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* <div className="text-center bg-gray-50 border border-gray-200 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Join Our Mission</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            We're always looking for talented individuals who share our passion for transforming investment discovery.
            If you're interested in joining our team, we'd love to hear from you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-[#40826D] text-white rounded-md hover:bg-[#40826D]/90 transition-all duration-300 font-semibold"
            >
              View Open Positions
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 border border-[#40826D] text-[#40826D] rounded-md hover:bg-[#40826D]/10 transition-all duration-300 font-semibold"
            >
              Contact Us
            </a>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default About;
