import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '@/components/Header';

const PrivacyPolicy = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="prose prose-lg max-w-none">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">
            {t('privacyPolicy.title')}
          </h1>
          
          <p className="text-gray-600 mb-8">
            {t('privacyPolicy.lastUpdated')}: {t('privacyPolicy.date')}
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.introduction.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('privacyPolicy.introduction.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.dataCollection.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                {t('privacyPolicy.dataCollection.content')}
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>{t('privacyPolicy.dataCollection.items.personal')}</li>
                <li>{t('privacyPolicy.dataCollection.items.professional')}</li>
                <li>{t('privacyPolicy.dataCollection.items.project')}</li>
                <li>{t('privacyPolicy.dataCollection.items.usage')}</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.dataUse.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                {t('privacyPolicy.dataUse.content')}
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>{t('privacyPolicy.dataUse.items.matching')}</li>
                <li>{t('privacyPolicy.dataUse.items.communication')}</li>
                <li>{t('privacyPolicy.dataUse.items.improvement')}</li>
                <li>{t('privacyPolicy.dataUse.items.legal')}</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.dataSharing.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('privacyPolicy.dataSharing.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.dataSecurity.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('privacyPolicy.dataSecurity.content')}
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.userRights.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                {t('privacyPolicy.userRights.content')}
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>{t('privacyPolicy.userRights.items.access')}</li>
                <li>{t('privacyPolicy.userRights.items.correction')}</li>
                <li>{t('privacyPolicy.userRights.items.deletion')}</li>
                <li>{t('privacyPolicy.userRights.items.portability')}</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('privacyPolicy.contact.title')}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('privacyPolicy.contact.content')}
              </p>
              <p className="text-gray-700 mt-4">
                <strong>{t('privacyPolicy.contact.email')}</strong>: <EMAIL>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
