
import React, { useState } from 'react';
import Header from '@/components/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, List, Settings } from 'lucide-react';
import UserSettings from './UserSettings';

const Dashboard = () => {
  const [activeView, setActiveView] = useState<'list' | 'settings'>('list');

  const renderListView = () => {
    const sampleData = [
      { id: 1, name: '<PERSON>', type: 'Angel Investor', location: 'San Francisco, CA', focus: 'SaaS, Fintech', deals: 15 },
      { id: 2, name: '<PERSON>', type: 'VC Partner', location: 'New York, NY', focus: 'AI, Healthcare', deals: 23 },
      { id: 3, name: '<PERSON>', type: 'Founder', location: 'Austin, TX', focus: 'EdTech Startup', stage: 'Series A' },
      { id: 4, name: '<PERSON>', type: 'Angel Investor', location: 'Seattle, WA', focus: 'Biotech, Climate', deals: 8 },
      { id: 5, name: '<PERSON>', type: 'Founder', location: 'Boston, MA', focus: 'HealthTech Startup', stage: 'Seed' },
    ];

    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Network Directory</h2>
        <div className="grid gap-4">
          {sampleData.map((person) => (
            <Card key={person.id} className="bg-white/90 backdrop-blur-sm border border-slate-200 hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold text-slate-800">{person.name}</h3>
                    <p className="text-teal-600 font-medium">{person.type}</p>
                    <p className="text-slate-600">{person.location}</p>
                    <p className="text-slate-700 mt-2">
                      {person.type === 'Founder' ? `Focus: ${person.focus}` : `Investment Focus: ${person.focus}`}
                    </p>
                  </div>
                  <div className="text-right">
                    {person.deals ? (
                      <div>
                        <span className="text-2xl font-bold text-slate-800">{person.deals}</span>
                        <p className="text-slate-600 text-sm">Deals</p>
                      </div>
                    ) : (
                      <div>
                        <span className="text-sm font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">
                          {person.stage}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="mt-4">
                  <Button size="sm" className="bg-teal-600 hover:bg-teal-700">
                    Connect
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-teal-100 via-blue-200 to-blue-600 relative overflow-hidden">
      {/* Wave patterns */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <svg className="absolute bottom-0 w-full h-2/3" viewBox="0 0 1200 600" preserveAspectRatio="none">
          <path d="M0,300 C300,200 600,400 900,250 C1050,150 1200,300 1200,300 L1200,600 L0,600 Z" fill="url(#wave1)" opacity="0.8"/>
          <path d="M0,400 C300,350 600,450 900,380 C1050,320 1200,400 1200,400 L1200,600 L0,600 Z" fill="url(#wave2)" opacity="0.6"/>
          <path d="M0,500 C300,450 600,520 900,480 C1050,440 1200,500 1200,500 L1200,600 L0,600 Z" fill="url(#wave3)" opacity="0.4"/>
          <defs>
            <linearGradient id="wave1" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#14b8a6" />
              <stop offset="100%" stopColor="#0891b2" />
            </linearGradient>
            <linearGradient id="wave2" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#0891b2" />
              <stop offset="100%" stopColor="#1e40af" />
            </linearGradient>
            <linearGradient id="wave3" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#1e40af" />
              <stop offset="100%" stopColor="#1e3a8a" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      <div className="relative z-10">
        <Header />
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Dashboard Header with Navigation */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-slate-800 mb-2">Dashboard</h1>
              <p className="text-slate-600">Explore your network and opportunities</p>
            </div>
            
            {/* Top Right Navigation */}
            <div className="flex space-x-2">
              <Button
                onClick={() => setActiveView('list')}
                variant={activeView === 'list' ? 'default' : 'outline'}
                className={activeView === 'list' ? 'bg-teal-600 hover:bg-teal-700' : ''}
              >
                <List className="w-4 h-4 mr-2" />
                List View
              </Button>
              <Button
                onClick={() => setActiveView('settings')}
                variant={activeView === 'settings' ? 'default' : 'outline'}
                className={activeView === 'settings' ? 'bg-teal-600 hover:bg-teal-700' : ''}
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Content Area */}
          <div className="bg-white/90 backdrop-blur-sm rounded-lg shadow-lg border border-slate-200 min-h-[600px]">
            <div className="p-6">
              {activeView === 'list' && renderListView()}
              {activeView === 'settings' && <UserSettings />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
