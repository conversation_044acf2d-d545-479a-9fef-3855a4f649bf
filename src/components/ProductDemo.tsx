import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
const ProductDemo = () => {
  return <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-slate-800/80 to-black/90"></div>
      
      {/* Grid overlay */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
        backgroundImage: `
            linear-gradient(rgba(0, 255, 170, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 170, 0.3) 1px, transparent 1px)
          `,
        backgroundSize: '40px 40px'
      }}></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            See the Future of Investment Discovery
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience our holographic interface that analyzes founder profiles, market trends, and investment opportunities in real-time
          </p>
        </div>

        {/* Main showcase container */}
        <div className="relative h-[600px] flex items-center justify-center mb-16">
          {/* Central holographic interface image */}
          <div className="relative z-20 max-w-4xl mx-auto">
            <div className="relative">
              <img src="/lovable-uploads/aca6c8ae-cb9d-4581-9fb5-ac2c4c13184b.png" alt="Holographic Investment Interface" className="w-full h-auto rounded-2xl shadow-2xl" style={{
              filter: 'drop-shadow(0 0 50px rgba(0, 255, 170, 0.3))'
            }} />
              
              {/* Overlay glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-transparent to-emerald-500/10 rounded-2xl"></div>
            </div>
          </div>

          {/* Floating connection lines and data points */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Animated connection lines */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 800 600">
              <defs>
                <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="rgba(0, 255, 170, 0.6)" />
                  <stop offset="100%" stopColor="rgba(0, 212, 170, 0.2)" />
                </linearGradient>
              </defs>
              
              {/* Animated paths connecting different points */}
              <path d="M100,150 Q300,100 500,200 T700,300" stroke="url(#lineGradient)" strokeWidth="2" fill="none" className="animate-pulse" style={{
              animationDuration: '3s'
            }} />
              <path d="M150,450 Q400,350 600,400 T750,500" stroke="url(#lineGradient)" strokeWidth="2" fill="none" className="animate-pulse" style={{
              animationDuration: '4s',
              animationDelay: '1s'
            }} />
              <path d="M50,300 Q200,250 400,350 T650,400" stroke="url(#lineGradient)" strokeWidth="2" fill="none" className="animate-pulse" style={{
              animationDuration: '5s',
              animationDelay: '2s'
            }} />
            </svg>

            {/* Floating data nodes */}
            {Array.from({
            length: 12
          }).map((_, i) => <div key={i} className="absolute w-3 h-3 bg-teal-400 rounded-full animate-pulse" style={{
            left: `${10 + Math.random() * 80}%`,
            top: `${10 + Math.random() * 80}%`,
            animationDelay: `${Math.random() * 3}s`,
            animationDuration: `${2 + Math.random() * 2}s`,
            boxShadow: '0 0 15px rgba(0, 255, 170, 0.8)'
          }} />)}
          </div>

          {/* Glowing center effect */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-96 h-96 bg-gradient-radial from-teal-400/10 to-transparent rounded-full blur-3xl animate-pulse"></div>
          </div>
        </div>

        {/* Feature highlights */}
        <div className="grid md:grid-cols-3 gap-8">
          
          
          
          
          
        </div>
      </div>
    </section>;
};
export default ProductDemo;