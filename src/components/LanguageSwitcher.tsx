import React from 'react';
import { useTranslation } from 'react-i18next';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
  };

  return (
    <Select value={i18n.language} onValueChange={handleLanguageChange}>
      <SelectTrigger className="w-20 h-8 bg-white/10 border-white/20 text-white hover:bg-white/20">
        <SelectValue>
          <div className="flex items-center space-x-1">
            <span className="text-sm">
              {i18n.language === 'en' ? '🇺🇸' : '🇨🇳'}
            </span>
            <span className="text-xs font-medium">
              {i18n.language === 'en' ? 'EN' : '中文'}
            </span>
          </div>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="en">
          <div className="flex items-center space-x-2">
            <span>🇺🇸</span>
            <span>English</span>
          </div>
        </SelectItem>
        <SelectItem value="zh">
          <div className="flex items-center space-x-2">
            <span>🇨🇳</span>
            <span>中文</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  );
};

export default LanguageSwitcher;
