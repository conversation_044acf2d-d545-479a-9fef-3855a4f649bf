
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { X, Building, User, Mail, MapPin, DollarSign, Briefcase } from 'lucide-react';

interface PricingTier {
  type: 'investor' | 'founder';
  tier: string;
  price: number;
  title: string;
  features: string[];
  highlighted?: boolean;
}

interface UserFormProps {
  userType: 'investor' | 'founder';
  tier: PricingTier;
  onClose: () => void;
}

const UserForm: React.FC<UserFormProps> = ({ userType, tier, onClose }) => {
  const [formData, setFormData] = useState({
    // Common fields
    name: '',
    email: '',
    company: '',
    location: '',
    bio: '',
    // Investor specific
    investmentFocus: '',
    portfolioSize: '',
    investmentRange: '',
    // Founder specific
    startupName: '',
    industry: '',
    stage: '',
    fundingNeeded: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the data to your backend
    console.log('Form submitted:', { userType, tier, formData });
    // For now, just show success and close
    alert('Registration successful! Welcome to Veridian Vista.');
    onClose();
  };

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <Card className="bg-gradient-to-br from-slate-800/90 to-teal-900/30 backdrop-blur-sm border border-teal-400/30 shadow-2xl shadow-teal-500/20">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl text-white flex items-center space-x-2">
                {userType === 'investor' ? <Briefcase className="w-6 h-6" /> : <Building className="w-6 h-6" />}
                <span>{tier.title} Registration</span>
              </CardTitle>
              <p className="text-teal-200 mt-2">
                Complete your profile to join our global network
              </p>
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={onClose}
              className="border-teal-400/50 text-teal-300 hover:bg-teal-500/20"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Common Fields */}
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-teal-200 flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span>Full Name</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="bg-slate-700/50 border-teal-500/30 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-teal-200 flex items-center space-x-1">
                  <Mail className="w-4 h-4" />
                  <span>Email</span>
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="bg-slate-700/50 border-teal-500/30 text-white"
                  required
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company" className="text-teal-200 flex items-center space-x-1">
                  <Building className="w-4 h-4" />
                  <span>{userType === 'investor' ? 'Investment Firm' : 'Company'}</span>
                </Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  className="bg-slate-700/50 border-teal-500/30 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location" className="text-teal-200 flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>Location</span>
                </Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="bg-slate-700/50 border-teal-500/30 text-white"
                  placeholder="City, Country"
                  required
                />
              </div>
            </div>

            {/* User Type Specific Fields */}
            {userType === 'investor' ? (
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="investmentFocus" className="text-teal-200">Investment Focus</Label>
                    <Input
                      id="investmentFocus"
                      value={formData.investmentFocus}
                      onChange={(e) => handleInputChange('investmentFocus', e.target.value)}
                      className="bg-slate-700/50 border-teal-500/30 text-white"
                      placeholder="e.g., AI, Fintech, Healthcare"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="portfolioSize" className="text-teal-200">Portfolio Size</Label>
                    <Input
                      id="portfolioSize"
                      value={formData.portfolioSize}
                      onChange={(e) => handleInputChange('portfolioSize', e.target.value)}
                      className="bg-slate-700/50 border-teal-500/30 text-white"
                      placeholder="e.g., 25+ companies"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="investmentRange" className="text-teal-200 flex items-center space-x-1">
                    <DollarSign className="w-4 h-4" />
                    <span>Investment Range</span>
                  </Label>
                  <Input
                    id="investmentRange"
                    value={formData.investmentRange}
                    onChange={(e) => handleInputChange('investmentRange', e.target.value)}
                    className="bg-slate-700/50 border-teal-500/30 text-white"
                    placeholder="e.g., $100K - $5M"
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startupName" className="text-teal-200">Startup Name</Label>
                    <Input
                      id="startupName"
                      value={formData.startupName}
                      onChange={(e) => handleInputChange('startupName', e.target.value)}
                      className="bg-slate-700/50 border-teal-500/30 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="industry" className="text-teal-200">Industry</Label>
                    <Input
                      id="industry"
                      value={formData.industry}
                      onChange={(e) => handleInputChange('industry', e.target.value)}
                      className="bg-slate-700/50 border-teal-500/30 text-white"
                      placeholder="e.g., SaaS, E-commerce, AI"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="stage" className="text-teal-200">Current Stage</Label>
                    <Input
                      id="stage"
                      value={formData.stage}
                      onChange={(e) => handleInputChange('stage', e.target.value)}
                      className="bg-slate-700/50 border-teal-500/30 text-white"
                      placeholder="e.g., Seed, Series A, MVP"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fundingNeeded" className="text-teal-200 flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>Funding Needed</span>
                    </Label>
                    <Input
                      id="fundingNeeded"
                      value={formData.fundingNeeded}
                      onChange={(e) => handleInputChange('fundingNeeded', e.target.value)}
                      className="bg-slate-700/50 border-teal-500/30 text-white"
                      placeholder="e.g., $500K"
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="bio" className="text-teal-200">
                {userType === 'investor' ? 'Investment Philosophy' : 'Company Description'}
              </Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                className="bg-slate-700/50 border-teal-500/30 text-white min-h-[100px]"
                placeholder={userType === 'investor' 
                  ? "Describe your investment approach and what you look for in startups..."
                  : "Tell us about your startup, mission, and what makes it unique..."
                }
              />
            </div>

            <div className="flex justify-between items-center pt-4">
              <div className="text-teal-200">
                <p className="font-medium">Plan: {tier.title}</p>
                <p className="text-sm">
                  {tier.price === 0 ? 'Free' : `$${tier.price}/month`}
                </p>
              </div>

              <Button 
                type="submit"
                className="bg-gradient-to-r from-teal-500 to-emerald-500 hover:from-teal-400 hover:to-emerald-400 text-white px-8 py-2"
              >
                Complete Registration
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserForm;
