
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, Crown } from 'lucide-react';

interface PricingTier {
  type: 'investor' | 'founder';
  tier: string;
  price: number;
  title: string;
  features: string[];
  highlighted?: boolean;
}

interface PricingCardProps {
  plan: PricingTier;
  onSelect: () => void;
}

const PricingCard: React.FC<PricingCardProps> = ({ plan, onSelect }) => {
  return (
    <Card className={`relative ${
      plan.highlighted 
        ? 'border-2 border-teal-400 bg-white/95 shadow-2xl shadow-teal-500/20' 
        : 'border border-teal-500/30 bg-white/90'
    } backdrop-blur-sm hover:scale-105 transition-transform`}>
      {plan.highlighted && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-teal-500 to-emerald-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
            <Crown className="w-4 h-4" />
            <span>Best Value</span>
          </div>
        </div>
      )}
      
      <CardHeader className="text-center">
        <CardTitle className="text-slate-800 text-xl">{plan.title}</CardTitle>
        <div className="text-center">
          {plan.price === 0 ? (
            <span className="text-3xl font-bold text-teal-600">Free</span>
          ) : (
            <>
              <span className="text-3xl font-bold text-slate-800">${plan.price}</span>
              <span className="text-slate-600">/month</span>
            </>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <ul className="space-y-3 mb-6">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <Check className="w-5 h-5 text-teal-500 mr-3 flex-shrink-0" />
              <span className="text-slate-700">{feature}</span>
            </li>
          ))}
        </ul>
        
        <Button 
          className={`w-full ${
            plan.highlighted
              ? 'bg-gradient-to-r from-teal-500 to-emerald-500 hover:from-teal-400 hover:to-emerald-400'
              : 'bg-teal-600 hover:bg-teal-700'
          } text-white shadow-lg`}
          onClick={onSelect}
        >
          {plan.price === 0 ? 'Get Started Free' : 'Select Plan'}
        </Button>
      </CardContent>
    </Card>
  );
};

export default PricingCard;
