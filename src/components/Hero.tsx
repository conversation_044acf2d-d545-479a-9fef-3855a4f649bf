
import React from 'react';

const Hero = () => {
  return (
    <section className="bg-gradient-to-br from-teal-900 via-emerald-900 to-slate-900 py-4 relative overflow-hidden min-h-[60vh]">
      {/* Enhanced futuristic shining dots background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 25 }).map((_, i) => (
          <div
            key={i}
            className="absolute bg-teal-400 rounded-full opacity-70 animate-pulse"
            style={{
              width: `${6 + Math.random() * 8}px`,
              height: `${6 + Math.random() * 8}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${1 + Math.random() * 2}s`,
              boxShadow: '0 0 15px rgba(45, 212, 191, 0.8)',
            }}
          />
        ))}
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={`bright-${i}`}
            className="absolute bg-emerald-300 rounded-full opacity-80 animate-pulse"
            style={{
              width: `${8 + Math.random() * 10}px`,
              height: `${8 + Math.random() * 10}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${0.8 + Math.random() * 1.5}s`,
              boxShadow: '0 0 20px rgba(16, 185, 129, 0.9)',
            }}
          />
        ))}
        {/* Large glowing orbs */}
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={`glow-${i}`}
            className="absolute bg-teal-300 rounded-full opacity-40 animate-pulse"
            style={{
              width: `${15 + Math.random() * 20}px`,
              height: `${15 + Math.random() * 20}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
              boxShadow: '0 0 30px rgba(45, 212, 191, 0.6)',
              filter: 'blur(2px)',
            }}
          />
        ))}
      </div>
    </section>
  );
};

export default Hero;
