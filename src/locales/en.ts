export default {
  "header": {
    "home": "Home",
    "founder": "Founder",
    "investor": "Investor",
    "about": "About Us",
    "backToHome": "Back to Home"
  },
  "founderForm": {
    "title": "Join Our Founder Network",
    "subtitle": "Connect with investors and fellow entrepreneurs. Share your vision and get discovered by the right partners who can help you build the future.",
    "headerTitle": "Founder Profile",
    "headerSubtitle": "Tell us about yourself and your project",
    "personalInfo": "Personal Information",
    "projectInfo": "Project Information",
    "additionalInfo": "Additional Information",
    "name": "Founder Name",
    "email": "Email Address",
    "linkedin": "LinkedIn Profile",
    "github": "GitHub Profile",
    "twitter": "Twitter Profile",
    "location": "Location",
    "projectName": "Project Name",
    "projectWebsite": "Project Website",
    "projectOneliner": "Project One-liner",
    "projectDescription": "Project Description",
    "projectStage": "Project Stage",
    "website": "Project Website",
    "oneliner": "Project One-liner",
    "description": "Project Description",
    "stage": "Project Stage",
    "industry": "Industry Verticals",
    "industryVerticals": "Industry Verticals",
    "currentTraction": "Current Traction Data",
    "traction": "Current Traction Data",
    "fundingNeeds": "Funding Needs",
    "funding": "Funding Needs",
    "wechatId": "WeChat ID",
    "wechatIdNote": "Only used for us to contact you, will not be made public",
    "projectWechatAccount": "Project WeChat Official Account",
    "projectShowcaseUrl": "Project Showcase URL (China)",
    "developerCommunityUrl": "Developer Community URL (China)"
  },
  "projectStage": {
    "idea": "Idea Stage",
    "mvp": "MVP",
    "early_traction": "Early Traction",
    "seed": "Seed Stage",
    "series_a": "Series A",
    "series_b": "Series B",
    "growth": "Growth Stage"
  },
  "industry": {
    "ai_ml": "AI/ML",
    "blockchain": "Blockchain",
    "fintech": "FinTech",
    "healthtech": "HealthTech",
    "edtech": "EdTech",
    "ecommerce": "E-commerce",
    "saas": "SaaS",
    "mobile_apps": "Mobile Apps",
    "iot": "IoT",
    "cybersecurity": "Cybersecurity",
    "cleantech": "CleanTech",
    "biotech": "BioTech",
    "gaming": "Gaming",
    "social_media": "Social Media",
    "marketplace": "Marketplace",
    "logistics": "Logistics",
    "real_estate": "Real Estate",
    "travel": "Travel",
    "other": "Other"
  },
  "funding": {
    "raising_funds": "Currently raising funds",
    "plan_to_raise": "Plan to raise in the future",
    "no_plan": "No fundraising plans"
  },
  "form": {
    "required": "*",
    "optional": "",
    "requiredNote": "Fields marked with a red asterisk (*) are required.",
    "submit": "Submit Application",
    "submitting": "Submitting...",
    "selectIndustry": "Select industry verticals",
    "selectStage": "Select project stage",
    "selectFunding": "Select funding status"
  },
  "placeholder": {
    "name": "Your full name",
    "email": "<EMAIL>",
    "linkedin": "https://linkedin.com/in/yourprofile",
    "github": "https://github.com/yourusername",
    "twitter": "https://twitter.com/yourusername",
    "location": "City, Country",
    "projectName": "Your project name",
    "projectWebsite": "https://yourproject.com",
    "projectOneliner": "Describe your project in one sentence",
    "projectDescription": "Provide a detailed description of your project, including the problem you're solving, your solution, target market, and any unique advantages you have.",
    "currentTraction": "Share key metrics, user numbers, revenue, partnerships, or other indicators of progress",
    "website": "https://yourproject.com",
    "oneliner": "Describe your project in one sentence",
    "description": "Provide a detailed description of your project, including the problem you're solving, your solution, target market, and any unique advantages you have.",
    "traction": "Share key metrics, user numbers, revenue, partnerships, or other indicators of progress",
    "wechatId": "Your WeChat ID",
    "projectWechatAccount": "WeChat Official Account name",
    "projectShowcaseUrl": "https://yourproject.cn",
    "developerCommunityUrl": "https://community.yourproject.cn"
  },
  "message": {
    "success": "Application submitted successfully! We'll be in touch soon.",
    "error": "An error occurred. Please try again."
  },
  "homepage": {
    "hero": {
      "title": "Be the smartest",
      "titleHighlight": "in the room.",
      "subtitle": "AI-powered investment intelligence that helps you discover breakthrough companies before they become obvious to everyone else.",
      "joinUs": "Join Us",
      "getStarted": "Get Started",
      "startDiscovering": "Start Discovering",
      "watchDemo": "Watch Demo"
    },
    "features": {
      "title": "AI-Powered Investment Intelligence",
      "subtitle": "Advanced technology that transforms how you discover and evaluate early-stage opportunities",
      "aiIntelligence": {
        "title": "Know who to back",
        "description": "Advanced algorithms analyze thousands of data points to identify promising opportunities"
      },
      "globalReach": {
        "title": "Global reach",
        "description": "Access investment opportunities worldwide, from emerging markets to established ecosystems"
      },
      "smartMatching": {
        "title": "Make the right decision faster",
        "description": "Intelligent matching connects you with companies that align with your investment thesis"
      },
      "dataInsights": {
        "title": "Maximize the value of your investments",
        "description": "Deep market insights and predictive analytics guide your investment decisions"
      },
      "realTimeData": {
        "title": "Maximize the value of your investments",
        "description": "Deep market insights and predictive analytics guide your investment decisions"
      },
      "aiFounderAnalysis": {
        "title": "AI Founder Analysis",
        "description": "Smart evaluation of entrepreneur backgrounds, track records, and potential using advanced machine learning algorithms"
      },
      "marketIntelligence": {
        "title": "Market Intelligence",
        "description": "Deep insights into emerging sectors, market trends, and competitive landscapes before they become mainstream"
      },
      "riskAssessment": {
        "title": "Risk Assessment",
        "description": "Predictive analytics and risk modeling to support confident investment decision-making at the earliest stages"
      }
    },
    "cta": {
      "title": "Ready to discover the next big opportunity?",
      "subtitle": "Join thousands of investors who trust Veridian Vista to identify breakthrough companies.",
      "getStarted": "Get Started Today",
      "learnMore": "Learn More"
    }
  },
  "navigation": {
    "home": "Home",
    "about": "About",
    "contact": "Contact"
  },
  "footer": {
    "company": "Company",
    "product": "Product",
    "resources": "Resources",
    "contact": "Contact",
    "description": "AI-Powered Early Investment Discovery Platform - connecting sophisticated investors with tomorrow's breakthrough companies.",
    "allRightsReserved": "All rights reserved.",
    "privacyPolicy": "Privacy Policy",
    "termsOfService": "Terms of Service",
    "security": "Security",
    "forInvestors": "For Investors",
    "forFounders": "For Founders",
    "aboutUs": "About Us"
  }
};
