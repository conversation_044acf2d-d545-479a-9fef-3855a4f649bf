
import './i18n';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Business from "./pages/Business";
import AngelVeins from "./pages/AngelVeins";
import FoundersLode from "./pages/FoundersLode";
import About from "./pages/About";
import Dashboard from "./pages/Dashboard";
import UserSettings from "./pages/UserSettings";
import InvestorForm from "./pages/InvestorForm";
import FounderForm from "./pages/FounderForm";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/business" element={<Business />} />
          <Route path="/angel-veins" element={<AngelVeins />} />
          <Route path="/founders-lode" element={<FoundersLode />} />
          <Route path="/about" element={<About />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/user-settings" element={<UserSettings />} />
          <Route path="/investor" element={<InvestorForm />} />
          <Route path="/founder" element={<FounderForm />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
